#ifndef AMSSDK_ENDPOINTS_H
#define AMSSDK_ENDPOINTS_H

#include <string>

namespace amssdk {
namespace endpoints {

constexpr const char* kChatMessages = "/chat-messages";
constexpr const char* kFileUpload = "/files/upload";
constexpr const char* kMessages = "/messages";
constexpr const char* kSuggested = "/suggested";
constexpr const char* kConversations = "/conversations";
constexpr const char* kAudioToText = "/audio-to-text";
constexpr const char* kTextToAudio = "/text-to-audio";
constexpr const char* kInfo = "/info";
constexpr const char* kParameters = "/parameters";
constexpr const char* kMeta = "/meta";
constexpr const char* kFeedbacks = "/feedbacks";
constexpr const char* kUsers = "/users";
constexpr const char* kStop = "/stop";

inline std::string TaskStop(const std::string& task_id) {
  return std::string(kChatMessages) + "/" + task_id + kStop;
}

inline std::string TaskFeedback(const std::string& message_id) {
  return std::string(kChatMessages) + "/" + message_id + kFeedbacks;
}

inline std::string Suggested(const std::string& message_id,
                             const std::string& user) {
  return std::string(kMessages) + "/" + message_id + kSuggested +
         "?user=" + user;
}

inline std::string Messages(const std::map<std::string, std::string>& query) {
  std::string endpoint = std::string(kMessages) + "?";
  for (const auto& pair : query) {
    endpoint += pair.first + "=" + pair.second + "&";
  }
  // remove last '&'
  endpoint.pop_back();
  return endpoint;
}

inline std::string Conversations(
    const std::map<std::string, std::string>& query) {
  std::string endpoint = std::string(kConversations) + "?";
  for (const auto& pair : query) {
    endpoint += pair.first + "=" + pair.second + "&";
  }
  // remove last '&'
  endpoint.pop_back();
  return endpoint;
}

inline std::string DeleteConversation(const std::string& conversation_id) {
  return std::string(kConversations) + "/" + conversation_id;
}
inline std::string RenameConversation(const std::string& conversation_id) {
  return std::string(kConversations) + "/" + conversation_id + "/name";
}
inline std::string AppMeta(const std::string& app_id) {
  return std::string(kMeta) + "?user=" + app_id;
}

}  // namespace endpoints
}  // namespace amssdk

#endif  // AMSSDK_ENDPOINTS_H
