#ifndef AMSSDK_CHAT_REQUEST_H
#define AMSSDK_CHAT_REQUEST_H

#include <string>
#include <vector>

#include "include/common/common.h"

namespace amssdk {

class ChatRequest {
 public:
  enum class ResponseMode { STREAMING, BLOCKING };
  ChatRequest() = default;

  // Builder methods
  ChatRequest& SetUser(const std::string& user) {
    user_ = user;
    return *this;
  }
  ChatRequest& SetQuery(const std::string& query) {
    query_ = query;
    return *this;
  }
  ChatRequest& SetInputs(const std::string& inputs) {
    inputs_ = inputs;
    return *this;
  }
  ChatRequest& SetConversationId(const std::string& id) {
    conversation_id_ = id;
    return *this;
  }
  ChatRequest& SetResponseMode(ResponseMode mode) {
    response_mode_ = mode;
    return *this;
  }
  ChatRequest& SetAutoGenerateName(bool auto_generate) {
    auto_generate_name_ = auto_generate;
    return *this;
  }
  ChatRequest& AddFile(const FileAttachment& file) {
    files_.push_back(file);
    return *this;
  }

  // Getters
  const std::string& GetUser() const noexcept { return user_; }
  const std::string& GetQuery() const noexcept { return query_; }
  const std::string& GetInputs() const noexcept { return inputs_; }
  const std::string& GetConversationId() const noexcept {
    return conversation_id_;
  }
  ResponseMode GetResponseMode() const noexcept { return response_mode_; }
  bool GetAutoGenerateName() const noexcept { return auto_generate_name_; }
  const std::vector<FileAttachment>& GetFiles() const noexcept {
    return files_;
  }

 private:
  std::string user_;
  std::string query_;
  std::string inputs_;
  std::string conversation_id_;
  bool auto_generate_name_ = false;
  std::vector<FileAttachment> files_;
  ResponseMode response_mode_ = ResponseMode::BLOCKING;
};
}  // namespace amssdk

#endif