#ifndef AMSSDK_DESERIALIZER_H
#define AMSSDK_DESERIALIZER_H

#include <memory>
#include <nlohmann/json.hpp>
#include <nlohmann/json_fwd.hpp>
#include <string>

#include "include/chat/chat_stream_event.h"
#include "stream_event_deserializer.h"

namespace amssdk {

class StreamEvent;
class FileResponse;
class SimpleResponse;
class ConversationResponse;
class SuggestedResponse;
class MessagesResponse;
class RenameConversationResponse;

// High-level deserialization interface (calls from_json internally)
FileResponse DeserializeFileResponse(const nlohmann::json& j);
ConversationResponse DeserializeConversationResponse(const nlohmann::json& j);
SuggestedResponse DeserializeSuggestedResponse(const nlohmann::json& j);
MessagesResponse DeserializeMessagesResponse(const nlohmann::json& j);
SimpleResponse DeserializeDeleteConversationResponse(const nlohmann::json& j);
RenameConversationResponse DeserializeRenameConversationResponse(
    const nlohmann::json& j);

// Legacy function names (for backward compatibility)
FileResponse FileResponseFromJson(const nlohmann::json& j);
ConversationResponse ConversationResponseFromJson(const nlohmann::json& j);
SuggestedResponse SuggestedResponseFromJson(const nlohmann::json& j);
MessagesResponse MessagesResponseFromJson(const nlohmann::json& j);
SimpleResponse DeleteConversationResponseFromJson(const nlohmann::json& j);
RenameConversationResponse RenameConversationResponseFromJson(
    const nlohmann::json& j);

}  // namespace amssdk
#endif  //AMSSDK_DESERIALIZER_H
