#include "deserializer.h"
#include <memory>
#include <stdexcept>
#include <nlohmann/json.hpp>

#include "include/chat/chat_stream_event.h"
#include "include/common/api_result.h"
#include "include/conversation/conversation_response.h"
#include "include/file.h"
#include "serializer_utils.h"

namespace amssdk {

namespace {
// 统一的反序列化错误处理
template<typename T>
T DeserializeWithErrorHandling(const nlohmann::json& j, const std::string& type_name) {
  try {
    T result;
    from_json(j, result);  // 调用底层from_json函数
    return result;
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON deserialization failed for " + type_name + ": " + e.what());
  } catch (const std::exception& e) {
    throw std::runtime_error("Deserialization failed for " + type_name + ": " + e.what());
  }
}
} // anonymous namespace

// High-level deserialization interface
FileResponse DeserializeFileResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<FileResponse>(j, "FileResponse");
}

ConversationResponse DeserializeConversationResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<ConversationResponse>(j, "ConversationResponse");
}

SuggestedResponse DeserializeSuggestedResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<SuggestedResponse>(j, "SuggestedResponse");
}

MessagesResponse DeserializeMessagesResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<MessagesResponse>(j, "MessagesResponse");
}

SimpleResponse DeserializeDeleteConversationResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<SimpleResponse>(j, "SimpleResponse");
}

RenameConversationResponse DeserializeRenameConversationResponse(const nlohmann::json& j) {
  return DeserializeWithErrorHandling<RenameConversationResponse>(j, "RenameConversationResponse");
}

// Legacy function names (for backward compatibility)
FileResponse FileResponseFromJson(const nlohmann::json& j) {
  return DeserializeFileResponse(j);
}
ConversationResponse ConversationResponseFromJson(const nlohmann::json& j) {
  return DeserializeConversationResponse(j);
}

SuggestedResponse SuggestedResponseFromJson(const nlohmann::json& j) {
  return DeserializeSuggestedResponse(j);
}

MessagesResponse MessagesResponseFromJson(const nlohmann::json& j) {
  return DeserializeMessagesResponse(j);
}

SimpleResponse DeleteConversationResponseFromJson(const nlohmann::json& j) {
  return DeserializeDeleteConversationResponse(j);
}

RenameConversationResponse RenameConversationResponseFromJson(const nlohmann::json& j) {
  return DeserializeRenameConversationResponse(j);
}

}  // namespace amssdk