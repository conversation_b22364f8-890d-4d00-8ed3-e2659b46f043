#include "deserializer.h"
#include <memory>
#include <nlohmann/json.hpp>

#include "include/chat/chat_stream_event.h"
#include "include/common/api_result.h"
#include "include/conversation/conversation_response.h"
#include "include/file.h"
#include "serializer_utils.h"

namespace amssdk {

FileResponse FileResponseFromJson(const nlohmann::json& j) {
  FileResponse resp;
  resp.file_type_ =
      SerializerUtils::FileTypeFromString(j.value("file_type", ""));
  resp.id_ = j.value("id", "");
  resp.name_ = j.value("name", "");
  resp.size_ = j.value("size", 0);
  resp.extension_ = j.value("extension", "");
  resp.mime_type_ = j.value("mime_type", "");
  resp.created_by_ = j.value("created_by", "");
  resp.created_at_ = j.value("created_at", 0);
  return resp;
}
ConversationResponse ConversationResponseFromJson(const nlohmann::json& j) {
  ConversationResponse resp;
  resp.limit = j.value("limit", 0);
  resp.has_more = j.value("has_more", false);
  if (j.contains("data")) {
    resp.data = j["data"].dump();
  }
  return resp;
}

SuggestedResponse SuggestedResponseFromJson(const nlohmann::json& j) {
  SuggestedResponse resp;
  resp.result = SimpleResponse::ResultType::kSuccess;
  resp.data = j.value("data", "");
  return resp;
}

MessagesResponse MessagesResponseFromJson(const nlohmann::json& j) {
  MessagesResponse resp;
  resp.limit = j.value("limit", 0);
  resp.has_more = j.value("has_more", false);
  if (j.contains("data")) {
    resp.data = j["data"].dump();
  }
  return resp;
}

SimpleResponse DeleteConversationResponseFromJson(const nlohmann::json& j) {
  SimpleResponse resp;
  resp.result = SimpleResponse::ResultType::kSuccess;
  return resp;
}

RenameConversationResponse RenameConversationResponseFromJson(
    const nlohmann::json& j) {
  RenameConversationResponse resp;
  resp.id = j.value("id", "");
  resp.name = j.value("name", "");
  if (j.contains("inputs")) {
    resp.inputs = j["inputs"].dump();
  }
  resp.status = j.value("status", "");
  if (j.contains("introduction")) {
    resp.introduction =
        j["introduction"].is_null() ? "" : j["introduction"].get<std::string>();
  }
  resp.created_at = j.value("created_at", 0);
  resp.updated_at = j.value("updated_at", 0);
  return resp;
}

}  // namespace amssdk