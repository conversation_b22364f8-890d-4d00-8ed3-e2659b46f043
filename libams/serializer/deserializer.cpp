#include "deserializer.h"
#include <memory>
#include <stdexcept>
#include <nlohmann/json.hpp>

#include "include/chat/chat_stream_event.h"
#include "include/common/api_result.h"
#include "include/conversation/conversation_response.h"
#include "include/file.h"
#include "serializer_utils.h"

namespace amssdk {

namespace {
// 统一的反序列化错误处理
template<typename T>
T DeserializeWithErrorHandling(const nlohmann::json& j, const std::string& type_name) {
  try {
    T result;
    // 手动反序列化，因为我们不使用from_json for responses
    return result;
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON deserialization failed for " + type_name + ": " + e.what());
  } catch (const std::exception& e) {
    throw std::runtime_error("Deserialization failed for " + type_name + ": " + e.what());
  }
}
} // anonymous namespace

FileResponse FileResponseFromJson(const nlohmann::json& j) {
  try {
    FileResponse resp;
    resp.file_type_ =
        SerializerUtils::FileTypeFromString(j.value("file_type", ""));
    resp.id_ = j.value("id", "");
    resp.name_ = j.value("name", "");
    resp.size_ = j.value("size", 0);
    resp.extension_ = j.value("extension", "");
    resp.mime_type_ = j.value("mime_type", "");
    resp.created_by_ = j.value("created_by", "");
    resp.created_at_ = j.value("created_at", 0);
    return resp;
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON deserialization failed for FileResponse: " + std::string(e.what()));
  } catch (const std::exception& e) {
    throw std::runtime_error("Deserialization failed for FileResponse: " + std::string(e.what()));
  }
}
ConversationResponse ConversationResponseFromJson(const nlohmann::json& j) {
  try {
    ConversationResponse resp;
    resp.limit = j.value("limit", 0);
    resp.has_more = j.value("has_more", false);
    if (j.contains("data")) {
      resp.data = j["data"].dump();
    }
    return resp;
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON deserialization failed for ConversationResponse: " + std::string(e.what()));
  } catch (const std::exception& e) {
    throw std::runtime_error("Deserialization failed for ConversationResponse: " + std::string(e.what()));
  }
}

SuggestedResponse SuggestedResponseFromJson(const nlohmann::json& j) {
  SuggestedResponse resp;
  resp.result = SimpleResponse::ResultType::kSuccess;
  resp.data = j.value("data", "");
  return resp;
}

MessagesResponse MessagesResponseFromJson(const nlohmann::json& j) {
  MessagesResponse resp;
  resp.limit = j.value("limit", 0);
  resp.has_more = j.value("has_more", false);
  if (j.contains("data")) {
    resp.data = j["data"].dump();
  }
  return resp;
}

SimpleResponse DeleteConversationResponseFromJson(const nlohmann::json& j) {
  SimpleResponse resp;
  resp.result = SimpleResponse::ResultType::kSuccess;
  return resp;
}

RenameConversationResponse RenameConversationResponseFromJson(
    const nlohmann::json& j) {
  RenameConversationResponse resp;
  resp.id = j.value("id", "");
  resp.name = j.value("name", "");
  if (j.contains("inputs")) {
    resp.inputs = j["inputs"].dump();
  }
  resp.status = j.value("status", "");
  if (j.contains("introduction")) {
    resp.introduction =
        j["introduction"].is_null() ? "" : j["introduction"].get<std::string>();
  }
  resp.created_at = j.value("created_at", 0);
  resp.updated_at = j.value("updated_at", 0);
  return resp;
}

}  // namespace amssdk