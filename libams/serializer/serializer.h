#ifndef AMSSDK_SERIALIZER_H
#define AMSSDK_SERIALIZER_H

#include <string>

namespace amssdk {

class RenameConversationRequest;
class DeleteConversationRequest;
class ConversationRequest;
class MessagesRequest;
class SuggestedRequest;
class ChatRequest;
class TaskStopRequest;
class FeedbackRequest;

std::string SerializeChatRequest(const ChatRequest& request);
std::string SerializeTaskStopRequest(const TaskStopRequest& request);
std::string SerializeFeedbackRequest(const FeedbackRequest& request);
std::string SerializeDeleteConversationRequest(
    const DeleteConversationRequest& request);
std::string SerializeRenameConversationRequest(
    const RenameConversationRequest& request);
std::string SerializeSuggestedRequest(const SuggestedRequest& request);
std::string SerializeMessagesRequest(const MessagesRequest& request);
std::string SerializeConversationRequest(const ConversationRequest& request);

}  // namespace amssdk

#endif  //AMSSDK_SERIALIZER_H
