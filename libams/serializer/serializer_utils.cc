#include "serializer_utils.h"
#include <nlohmann/json.hpp>

#include "include/task.h"
#include "include/file.h"
#include "include/conversation/conversation_response.h"
#include "include/common/common.h"

namespace amssdk {

std::string SerializerUtils::ResponseModeToString(
    const ChatRequest::ResponseMode& mode) {
  switch (mode) {
    case ChatRequest::ResponseMode::STREAMING:
      return "streaming";
    case ChatRequest::ResponseMode::BLOCKING:
      return "blocking";
    default:
      return "streaming";
  }
}
std::string SerializerUtils::FileTypeToString(
    const FileAttachment::FileType& type) {
  switch (type) {
    case FileAttachment::FileType::IMAGE:
      return "image";
    case FileAttachment::FileType::VIDEO:
      return "video";
    case FileAttachment::FileType::AUDIO:
      return "audio";
    case FileAttachment::FileType::DOCUMENT:
      return "document";
    default:
      return "image";
  }
}

std::string SerializerUtils::TransferMethodToString(
    const FileAttachment::TransferMethod& method) {
  switch (method) {
    case FileAttachment::TransferMethod::LOCAL_FILE:
      return "local_file";
    case FileAttachment::TransferMethod::URL:
      return "url";
    default:
      return "local_file";
  }
}
ChatRequest::ResponseMode SerializerUtils::ResponseModeFromString(
    const std::string& mode) {
  if (mode == "streaming") {
    return ChatRequest::ResponseMode::STREAMING;
  }
  return ChatRequest::ResponseMode::BLOCKING;
}
FileAttachment::FileType SerializerUtils::FileTypeFromString(
    const std::string& type) {
  if (type == "image") {
    return FileAttachment::FileType::IMAGE;
  }
  if (type == "video") {
    return FileAttachment::FileType::VIDEO;
  }
  if (type == "audio") {
    return FileAttachment::FileType::AUDIO;
  }
  if (type == "document") {
    return FileAttachment::FileType::DOCUMENT;
  }
  return FileAttachment::FileType::IMAGE;
}
FileAttachment::TransferMethod SerializerUtils::TransferMethodFromString(
    const std::string& method) {
  if (method == "local_file") {
    return FileAttachment::TransferMethod::LOCAL_FILE;
  }
  return FileAttachment::TransferMethod::URL;
}

std::string SerializerUtils::SortRuleToString(
    const ConversationRequest::SortRule& rule) {
  if (rule == ConversationRequest::SortRule::kCreatedAt) {
    return "created_at";
  }
  if (rule == ConversationRequest::SortRule::kUpdatedAt) {
    return "updated_at";
  }
  if (rule == ConversationRequest::SortRule::kDescCreatedAt) {
    return "-created_at";
  }
  if (rule == ConversationRequest::SortRule::kDescUpdatedAt) {
    return "-updated_at";
  }
  return "updated_at";
}

ConversationRequest::SortRule SerializerUtils::SortRuleFromString(
    const std::string& rule) {
  if (rule == "created_at")
    return ConversationRequest::SortRule::kCreatedAt;
  if (rule == "updated_at")
    return ConversationRequest::SortRule::kUpdatedAt;
  if (rule == "-created_at")
    return ConversationRequest::SortRule::kDescCreatedAt;
  if (rule == "-updated_at")
    return ConversationRequest::SortRule::kDescUpdatedAt;
  return ConversationRequest::SortRule::kUpdatedAt;
}

// to_json implementations for request models (serialization only)
void to_json(nlohmann::json& j, const ChatRequest& r) {
  j = nlohmann::json::object();
  // Maintain existing behavior: always include keys even if empty
  j["inputs"] = r.GetInputs();
  j["query"] = r.GetQuery();
  j["response_mode"] =
      SerializerUtils::ResponseModeToString(r.GetResponseMode());
  j["conversation_id"] = r.GetConversationId();
  j["user"] = r.GetUser();

  // files
  nlohmann::json files = nlohmann::json::array();
  for (const auto& f : r.GetFiles()) {
    nlohmann::json jf;
    jf["type"] = SerializerUtils::FileTypeToString(f.type);
    jf["transfer_method"] =
        SerializerUtils::TransferMethodToString(f.transfer_method);
    if (f.transfer_method == FileAttachment::TransferMethod::LOCAL_FILE) {
      jf["upload_file_id"] = f.upload_file_id;
    } else {
      jf["url"] = f.url;
    }
    files.push_back(std::move(jf));
  }
  j["files"] = std::move(files);
}



void to_json(nlohmann::json& j, const SuggestedRequest& r) {
  j = nlohmann::json::object();
  j["message_id"] = r.GetMessageId();
  j["user"] = r.GetUser();
}


void to_json(nlohmann::json& j, const MessagesRequest& r) {
  j = nlohmann::json::object();
  j["conversation_id"] = r.GetConversationId();
  j["user"] = r.GetUser();
  j["first_id"] = r.GetFirstId();
  j["limit"] = r.GetLimit();
}


void to_json(nlohmann::json& j, const ConversationRequest& r) {
  j = nlohmann::json::object();
  j["user"] = r.GetUser();
  j["last_id"] = r.GetLastId();
  j["limit"] = r.GetLimit();
  j["sort_by"] = SerializerUtils::SortRuleToString(r.GetSortRule());
}


void to_json(nlohmann::json& j, const DeleteConversationRequest& r) {
  j = nlohmann::json::object();
  j["user"] = r.GetUser();
  j["conversation_id"] = r.GetConversationId();
}


void to_json(nlohmann::json& j, const RenameConversationRequest& r) {
  j = nlohmann::json::object();
  j["name"] = r.GetName();
  j["user"] = r.GetUser();
  j["auto_generate_name"] = r.GetAutoGenerateName();
  j["conversation_id"] = r.GetConversationId();
}


void to_json(nlohmann::json& j, const TaskStopRequest& r) {
  j = nlohmann::json::object();
  j["task_id"] = r.GetTaskId();
  j["user"] = r.GetUser();
}

void to_json(nlohmann::json& j, const FeedbackRequest& r) {
  j = nlohmann::json::object();
  j["message_id"] = r.GetMessageId();
  j["user"] = r.GetUser();
  j["content"] = r.GetContent();

  // Convert Rating enum to string
  std::string rating_str;
  switch (r.GetRating()) {
    case FeedbackRequest::Rating::kLike:
      rating_str = "like";
      break;
    case FeedbackRequest::Rating::kDislike:
      rating_str = "dislike";
      break;
    case FeedbackRequest::Rating::kNull:
    default:
      rating_str = "null";
      break;
  }
  j["rating"] = rating_str;
}

// from_json implementations for response models (deserialization only)
void from_json(const nlohmann::json& j, FileResponse& r) {
  r.file_type_ = SerializerUtils::FileTypeFromString(j.value("file_type", ""));
  r.id_ = j.value("id", "");
  r.name_ = j.value("name", "");
  r.size_ = j.value("size", 0);
  r.extension_ = j.value("extension", "");
  r.mime_type_ = j.value("mime_type", "");
  r.created_by_ = j.value("created_by", "");
  r.created_at_ = j.value("created_at", 0);
}

void from_json(const nlohmann::json& j, ConversationResponse& r) {
  r.limit = j.value("limit", 0);
  r.has_more = j.value("has_more", false);
  if (j.contains("data")) {
    r.data = j["data"].dump();
  }
}

void from_json(const nlohmann::json& j, SuggestedResponse& r) {
  r.result = SimpleResponse::ResultType::kSuccess;
  r.data = j.value("data", "");
}

void from_json(const nlohmann::json& j, MessagesResponse& r) {
  r.limit = j.value("limit", 0);
  r.has_more = j.value("has_more", false);
  if (j.contains("data")) {
    r.data = j["data"].dump();
  }
}

void from_json(const nlohmann::json& j, SimpleResponse& r) {
  r.result = SimpleResponse::ResultType::kSuccess;
}

void from_json(const nlohmann::json& j, RenameConversationResponse& r) {
  r.id = j.value("id", "");
  r.name = j.value("name", "");
  if (j.contains("inputs")) {
    r.inputs = j["inputs"].dump();
  }
  r.status = j.value("status", "");
  if (j.contains("introduction")) {
    r.introduction = j["introduction"].is_null() ? "" : j["introduction"].get<std::string>();
  }
  r.created_at = j.value("created_at", 0);
  r.updated_at = j.value("updated_at", 0);
}

}  // namespace amssdk
