#ifndef AMSSDK_SERIALIZER_UTILS_H
#define AMSSDK_SERIALIZER_UTILS_H
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include "include/chat/chat_request.h"
#include "include/conversation/conversation_request.h"

namespace amssdk {

// Forward declarations
class TaskStopRequest;
class FeedbackRequest;
class SuggestedRequest;
class MessagesRequest;
class DeleteConversationRequest;
class RenameConversationRequest;

// Response types
class FileResponse;
class ConversationResponse;
class SuggestedResponse;
class MessagesResponse;
class SimpleResponse;
class RenameConversationResponse;
class SerializerUtils {
 public:
  SerializerUtils() = delete;
  ~SerializerUtils() = delete;
  SerializerUtils(const SerializerUtils&) = delete;
  SerializerUtils& operator=(const SerializerUtils&) = delete;
  SerializerUtils(SerializerUtils&&) = delete;

  // Enum <-> string mappings
  static std::string ResponseModeToString(
      const ChatRequest::ResponseMode& mode);
  static std::string FileTypeToString(const FileAttachment::FileType& type);
  static std::string TransferMethodToString(
      const FileAttachment::TransferMethod& method);
  static ChatRequest::ResponseMode ResponseModeFromString(
      const std::string& mode);
  static FileAttachment::FileType FileTypeFromString(const std::string& type);
  static FileAttachment::TransferMethod TransferMethodFromString(
      const std::string& method);
  static std::string SortRuleToString(
      const ConversationRequest::SortRule& rule);
  static ConversationRequest::SortRule SortRuleFromString(
      const std::string& rule);
};

// to_json declarations for request models (serialization only)
void to_json(nlohmann::json& j, const ChatRequest& r);
void to_json(nlohmann::json& j, const SuggestedRequest& r);
void to_json(nlohmann::json& j, const MessagesRequest& r);
void to_json(nlohmann::json& j, const ConversationRequest& r);
void to_json(nlohmann::json& j, const DeleteConversationRequest& r);
void to_json(nlohmann::json& j, const RenameConversationRequest& r);
void to_json(nlohmann::json& j, const TaskStopRequest& r);
void to_json(nlohmann::json& j, const FeedbackRequest& r);

// from_json declarations for response models (deserialization only)
void from_json(const nlohmann::json& j, FileResponse& r);
void from_json(const nlohmann::json& j, ConversationResponse& r);
void from_json(const nlohmann::json& j, SuggestedResponse& r);
void from_json(const nlohmann::json& j, MessagesResponse& r);
void from_json(const nlohmann::json& j, SimpleResponse& r);
void from_json(const nlohmann::json& j, RenameConversationResponse& r);

}  // namespace amssdk

#endif  //AMSSDK_SERIALIZER_UTILS_H
