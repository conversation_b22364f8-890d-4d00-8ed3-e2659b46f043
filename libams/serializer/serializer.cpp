#include "serializer.h"
#include <nlohmann/json.hpp>
#include "../include/task.h"
#include "include/chat/chat_request.h"
#include "include/conversation/conversation_request.h"
#include "serializer_utils.h"
#include <stdexcept>

namespace amssdk {

namespace {
// 统一的序列化错误处理
template<typename T>
std::string SerializeWithErrorHandling(const T& request, const std::string& request_type) {
  try {
    nlohmann::json json_request;
    to_json(json_request, request);
    return json_request.dump();
  } catch (const nlohmann::json::exception& e) {
    throw std::runtime_error("JSON serialization failed for " + request_type + ": " + e.what());
  } catch (const std::exception& e) {
    throw std::runtime_error("Serialization failed for " + request_type + ": " + e.what());
  }
}
} // anonymous namespace

std::string SerializeChatRequest(const ChatRequest& request) {
  return SerializeWithErrorHandling(request, "ChatRequest");
}

std::string SerializeTaskStopRequest(const TaskStopRequest& request) {
  return SerializeWithErrorHandling(request, "TaskStopRequest");
}

std::string SerializeFeedbackRequest(const FeedbackRequest& request) {
  return SerializeWithErrorHandling(request, "FeedbackRequest");
}

std::string SerializeDeleteConversationRequest(
    const DeleteConversationRequest& request) {
  return SerializeWithErrorHandling(request, "DeleteConversationRequest");
}

std::string SerializeRenameConversationRequest(
    const RenameConversationRequest& request) {
  return SerializeWithErrorHandling(request, "RenameConversationRequest");
}

std::string SerializeSuggestedRequest(const SuggestedRequest& request) {
  return SerializeWithErrorHandling(request, "SuggestedRequest");
}

std::string SerializeMessagesRequest(const MessagesRequest& request) {
  return SerializeWithErrorHandling(request, "MessagesRequest");
}

std::string SerializeConversationRequest(const ConversationRequest& request) {
  return SerializeWithErrorHandling(request, "ConversationRequest");
}

}  // namespace amssdk
