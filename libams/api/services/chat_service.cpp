#include "chat_service.h"
#include "api/api_manager.h"
#include "include/common/common.h"
#include "include/common/endpoints.h"
#include "serializer/deserializer.h"
#include "serializer/serializer.h"
#include <vector>
#include <memory>

namespace amssdk {

ChatService::ChatService(ApiManager& api_manager)
    : http_client_(api_manager.GetHttpClient()),
      auth_(api_manager.GetAuthorization()) {}

ApiResult<void> ChatService::SendChatMessage(
    const ChatRequest& request,
    const StreamEventCallback& stream_event_callback) const {

  const std::string json_data = SerializeChatRequest(request);

  // Simple frame buffer to handle SSE-style chunks (frames separated by blank lines)
  struct StreamFrameBuffer {
    std::string buf;
    std::vector<std::string> feed(const std::string& chunk) {
      buf += chunk;
      std::vector<std::string> frames;
      size_t pos = std::string::npos;
      while ((pos = buf.find("\n\n")) != std::string::npos) {
        std::string frame = buf.substr(0, pos);
        buf.erase(0, pos + 2);
        if (!frame.empty()) frames.push_back(std::move(frame));
      }
      return frames;
    }
  };
  auto frame_buffer = std::make_shared<StreamFrameBuffer>();

  auto callback = [stream_event_callback, frame_buffer](const std::string& data,
                                          intptr_t userdata) {
    (void)userdata;
    StreamEventDeserializer deserializer;

    // Accumulate and process complete frames
    for (auto& frame : frame_buffer->feed(data)) {
      auto event_type = deserializer.DeserializeStreamEvent(frame);
      if (event_type && stream_event_callback) {
        stream_event_callback(std::move(event_type));
      }
    }
    return true;
  };

  auto chat_result =
      http_client_.Post(json_data, endpoints::kChatMessages, callback);

  return BuildResult<void>(chat_result);
}

}  // namespace amssdk
