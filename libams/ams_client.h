#ifndef AMSSDK_AMS_CLIENT_H
#define AMSSDK_AMS_CLIENT_H

#include <functional>
#include <memory>
#include <string>

#include "api/services/app_service.h"

#include "include/app.h"
#include "include/chat/chat_request.h"
#include "include/chat/chat_stream_event.h"
#include "include/common/api_result.h"
#include "include/conversation/conversation_request.h"
#include "include/conversation/conversation_response.h"
#include "include/file.h"
#include "include/task.h"

namespace amssdk {
class AppInfoRequest;
}
namespace amssdk {
class AppMetaResponse;
class AppMetaRequest;
class AudioToTextRequest;
class AudioToTextResponse;

class ApiManager;

class AmsClient {
 public:
  LIBAMS_EXPORT
  explicit AmsClient(const std::string& base_url);

  LIBAMS_EXPORT ~AmsClient();

  LIBAMS_EXPORT bool SetAuthorizationKey(const std::string& key) const;
  LIBAMS_EXPORT void SetMaxTimeout(int32_t ms) const;

  using StreamEventCallback = std::function<void(std::unique_ptr<StreamEvent>)>;
  LIBAMS_EXPORT ApiResult<void> SendChatMessage(
      const ChatRequest& request, const StreamEventCallback& callback) const;
  LIBAMS_EXPORT ApiResult<FileResponse> FileUpload(
      const FileRequest& request) const;
  LIBAMS_EXPORT ApiResult<SimpleResponse> StopTask(
      const TaskStopRequest& request) const;
  LIBAMS_EXPORT ApiResult<SimpleResponse> SendFeedback(
      const FeedbackRequest& request) const;
  LIBAMS_EXPORT ApiResult<SuggestedResponse> GetSuggested(
      const SuggestedRequest& request) const;
  LIBAMS_EXPORT ApiResult<MessagesResponse> GetMessages(
      const MessagesRequest& request) const;
  LIBAMS_EXPORT ApiResult<ConversationResponse> GetConversation(
      const ConversationRequest& request) const;
  LIBAMS_EXPORT ApiResult<SimpleResponse> DeleteConversation(
      const DeleteConversationRequest& request) const;
  LIBAMS_EXPORT ApiResult<RenameConversationResponse> RenameConversation(
      const RenameConversationRequest& request) const;
  LIBAMS_EXPORT ApiResult<AudioToTextResponse> AudioToText(
      const AudioToTextRequest& request) const;

  LIBAMS_EXPORT ApiResult<AppMetaResponse> AppMeta(
      const AppMetaRequest& request) const;
  LIBAMS_EXPORT ApiResult<AppInfoResponse> AppInfo(
      const AppInfoRequest& request) const;
  LIBAMS_EXPORT ApiResult<AppParamResponse> AppParameters(
      const AppParamRequest& request) const;

 private:
  std::unique_ptr<ApiManager> api_manager_;
};

}  // namespace amssdk

#endif  // AMSSDK_AMS_CLIENT_H
