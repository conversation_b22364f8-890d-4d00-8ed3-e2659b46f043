#include <iostream>
#include <string>
#include "libams/serializer/serializer.h"
#include "libams/include/task.h"
#include "libams/include/chat/chat_request.h"
#include "libams/include/conversation/conversation_request.h"

using namespace amssdk;

void TestTaskStopRequestSerialization() {
    std::cout << "=== Testing TaskStopRequest Serialization ===" << std::endl;
    
    TaskStopRequest request;
    request.SetTaskId("task_12345")
           .SetUser("test_user");
    
    std::string json = SerializeTaskStopRequest(request);
    std::cout << "Serialized JSON: " << json << std::endl;
    
    // Expected: {"task_id":"task_12345","user":"test_user"}
    if (json.find("\"task_id\":\"task_12345\"") != std::string::npos &&
        json.find("\"user\":\"test_user\"") != std::string::npos) {
        std::cout << "✓ TaskStopRequest serialization PASSED" << std::endl;
    } else {
        std::cout << "✗ TaskStopRequest serialization FAILED" << std::endl;
    }
}

void TestFeedbackRequestSerialization() {
    std::cout << "\n=== Testing FeedbackRequest Serialization ===" << std::endl;
    
    FeedbackRequest request;
    request.SetMessageId("msg_67890")
           .SetUser("test_user")
           .SetContent("Great response!")
           .SetRating(FeedbackRequest::Rating::kLike);
    
    std::string json = SerializeFeedbackRequest(request);
    std::cout << "Serialized JSON: " << json << std::endl;
    
    // Expected: Contains like rating
    if (json.find("\"rating\":\"like\"") != std::string::npos &&
        json.find("\"message_id\":\"msg_67890\"") != std::string::npos) {
        std::cout << "✓ FeedbackRequest serialization PASSED" << std::endl;
    } else {
        std::cout << "✗ FeedbackRequest serialization FAILED" << std::endl;
    }
}

void TestChatRequestSerialization() {
    std::cout << "\n=== Testing ChatRequest Serialization ===" << std::endl;
    
    ChatRequest request;
    request.SetUser("test_user")
           .SetQuery("Hello, how are you?")
           .SetInputs("{}")
           .SetConversationId("conv_123")
           .SetResponseMode(ChatRequest::ResponseMode::STREAMING)
           .SetAutoGenerateName(true);
    
    std::string json = SerializeChatRequest(request);
    std::cout << "Serialized JSON: " << json << std::endl;
    
    // Check key fields
    if (json.find("\"user\":\"test_user\"") != std::string::npos &&
        json.find("\"query\":\"Hello, how are you?\"") != std::string::npos &&
        json.find("\"response_mode\":\"streaming\"") != std::string::npos) {
        std::cout << "✓ ChatRequest serialization PASSED" << std::endl;
    } else {
        std::cout << "✗ ChatRequest serialization FAILED" << std::endl;
    }
}

void TestErrorHandling() {
    std::cout << "\n=== Testing Error Handling ===" << std::endl;
    
    try {
        // Test with potentially problematic data
        TaskStopRequest request;
        request.SetTaskId("")
               .SetUser("");
        
        std::string json = SerializeTaskStopRequest(request);
        std::cout << "Empty fields serialized: " << json << std::endl;
        std::cout << "✓ Error handling test PASSED" << std::endl;
    } catch (const std::exception& e) {
        std::cout << "Exception caught: " << e.what() << std::endl;
        std::cout << "✓ Error handling working correctly" << std::endl;
    }
}

int main() {
    std::cout << "Starting Serialization Tests..." << std::endl;
    
    TestTaskStopRequestSerialization();
    TestFeedbackRequestSerialization();
    TestChatRequestSerialization();
    TestErrorHandling();
    
    std::cout << "\n=== All Tests Completed ===" << std::endl;
    return 0;
}