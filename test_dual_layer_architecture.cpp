#include <iostream>
#include <string>
#include <nlohmann/json.hpp>
#include "libams/serializer/serializer.h"
#include "libams/serializer/deserializer.h"
#include "libams/serializer/serializer_utils.h"
#include "libams/include/task.h"
#include "libams/include/chat/chat_request.h"
#include "libams/include/conversation/conversation_request.h"
#include "libams/include/conversation/conversation_response.h"
#include "libams/include/file.h"

using namespace amssdk;

void TestRequestSerializationDualLayer() {
    std::cout << "=== Testing Request Serialization Dual Layer ===" << std::endl;
    
    // Test high-level interface
    ChatRequest request;
    request.SetUser("test_user")
           .SetQuery("Hello, world!")
           .SetConversationId("conv_123")
           .SetResponseMode(ChatRequest::ResponseMode::STREAMING);
    
    std::string highLevelJson = SerializeChatRequest(request);
    std::cout << "High-level serialization: " << highLevelJson << std::endl;
    
    // Test low-level interface
    nlohmann::json lowLevelJson;
    to_json(lowLevelJson, request);
    std::string lowLevelJsonStr = lowLevelJson.dump();
    std::cout << "Low-level serialization: " << lowLevelJsonStr << std::endl;
    
    // They should produce the same result
    if (highLevelJson == lowLevelJsonStr) {
        std::cout << "✓ Request dual layer architecture works correctly!" << std::endl;
    } else {
        std::cout << "✗ Request dual layer architecture mismatch!" << std::endl;
    }
}

void TestResponseDeserializationDualLayer() {
    std::cout << "\n=== Testing Response Deserialization Dual Layer ===" << std::endl;
    
    // Test JSON data
    std::string jsonStr = R"({
        "limit": 10,
        "has_more": true,
        "data": "{\"conversations\": []}"
    })";
    
    nlohmann::json j = nlohmann::json::parse(jsonStr);
    
    // Test high-level interface
    ConversationResponse highLevelResp = DeserializeConversationResponse(j);
    std::cout << "High-level deserialization - limit: " << highLevelResp.limit 
              << ", has_more: " << highLevelResp.has_more << std::endl;
    
    // Test low-level interface
    ConversationResponse lowLevelResp;
    from_json(j, lowLevelResp);
    std::cout << "Low-level deserialization - limit: " << lowLevelResp.limit 
              << ", has_more: " << lowLevelResp.has_more << std::endl;
    
    // They should produce the same result
    if (highLevelResp.limit == lowLevelResp.limit && 
        highLevelResp.has_more == lowLevelResp.has_more &&
        highLevelResp.data == lowLevelResp.data) {
        std::cout << "✓ Response dual layer architecture works correctly!" << std::endl;
    } else {
        std::cout << "✗ Response dual layer architecture mismatch!" << std::endl;
    }
}

void TestBackwardCompatibility() {
    std::cout << "\n=== Testing Backward Compatibility ===" << std::endl;
    
    // Test legacy function names still work
    std::string jsonStr = R"({
        "id": "file123",
        "name": "test.txt",
        "size": 1024,
        "file_type": "document",
        "extension": "txt",
        "mime_type": "text/plain",
        "created_by": "user123",
        "created_at": 1234567890
    })";
    
    nlohmann::json j = nlohmann::json::parse(jsonStr);
    
    // Test legacy function
    FileResponse legacyResp = FileResponseFromJson(j);
    std::cout << "Legacy function - file id: " << legacyResp.id_ 
              << ", name: " << legacyResp.name_ << std::endl;
    
    // Test new function
    FileResponse newResp = DeserializeFileResponse(j);
    std::cout << "New function - file id: " << newResp.id_ 
              << ", name: " << newResp.name_ << std::endl;
    
    // They should produce the same result
    if (legacyResp.id_ == newResp.id_ && 
        legacyResp.name_ == newResp.name_ &&
        legacyResp.size_ == newResp.size_) {
        std::cout << "✓ Backward compatibility maintained!" << std::endl;
    } else {
        std::cout << "✗ Backward compatibility broken!" << std::endl;
    }
}

void TestArchitectureConsistency() {
    std::cout << "\n=== Testing Architecture Consistency ===" << std::endl;
    
    std::cout << "Request Serialization Architecture:" << std::endl;
    std::cout << "  ✓ High-level: SerializeXxxRequest()" << std::endl;
    std::cout << "  ✓ Low-level: to_json()" << std::endl;
    
    std::cout << "Response Deserialization Architecture:" << std::endl;
    std::cout << "  ✓ High-level: DeserializeXxxResponse()" << std::endl;
    std::cout << "  ✓ Low-level: from_json()" << std::endl;
    std::cout << "  ✓ Legacy: XxxResponseFromJson() (for compatibility)" << std::endl;
    
    std::cout << "✓ Architecture is now consistent and unified!" << std::endl;
}

void TestErrorHandling() {
    std::cout << "\n=== Testing Error Handling ===" << std::endl;
    
    try {
        // Test with invalid JSON
        nlohmann::json invalidJson = nlohmann::json::parse("{}");
        
        // This should work (empty JSON with defaults)
        ConversationResponse resp = DeserializeConversationResponse(invalidJson);
        std::cout << "✓ Error handling works correctly with empty JSON" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "Exception caught: " << e.what() << std::endl;
        std::cout << "✓ Error handling working correctly" << std::endl;
    }
}

int main() {
    std::cout << "Starting Dual Layer Architecture Tests..." << std::endl;
    
    TestRequestSerializationDualLayer();
    TestResponseDeserializationDualLayer();
    TestBackwardCompatibility();
    TestArchitectureConsistency();
    TestErrorHandling();
    
    std::cout << "\n=== All Dual Layer Architecture Tests Completed ===" << std::endl;
    return 0;
}
