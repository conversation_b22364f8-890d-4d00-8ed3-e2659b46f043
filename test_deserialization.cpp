#include <iostream>
#include <string>
#include "libams/serializer/deserializer.h"
#include "libams/include/chat/chat_stream_event.h"
#include "libams/include/file.h"

using namespace amssdk;

void TestStreamEventDeserialization() {
    std::cout << "=== Testing Stream Event Deserialization ===" << std::endl;
    
    // Test various event types
    std::string messageEvent = R"({"event":"message","task_id":"task123","message_id":"msg456","conversation_id":"conv789","answer":"Hello World"})";
    std::string agentThoughtEvent = R"({"event":"agent_thought","id":"thought1","task_id":"task123","message_id":"msg456","conversation_id":"conv789","thought":"Processing...","tool":"calculator","tool_input":"2+2"})";
    std::string errorEvent = R"({"event":"error","task_id":"task123","message_id":"msg456","status":500,"code":"INTERNAL_ERROR","message":"Something went wrong"})";
    
    // Test with SSE format
    std::string sseMessageEvent = "data: " + messageEvent + "\n\n";
    
    StreamEventDeserializer deserializer;
    
    auto event1 = deserializer.DeserializeStreamEvent(sseMessageEvent);
    if (event1) {
        std::cout << "✓ SSE format message event parsed successfully" << std::endl;
        std::cout << "  Event type: " << static_cast<int>(event1->GetType()) << std::endl;
    } else {
        std::cout << "✗ SSE format message event parsing failed" << std::endl;
    }
    
    auto event2 = deserializer.DeserializeStreamEvent(messageEvent);
    if (event2) {
        std::cout << "✓ Direct JSON message event parsed successfully" << std::endl;
    } else {
        std::cout << "✗ Direct JSON message event parsing failed" << std::endl;
    }
    
    auto event3 = deserializer.DeserializeStreamEvent(agentThoughtEvent);
    if (event3) {
        std::cout << "✓ Agent thought event parsed successfully" << std::endl;
    } else {
        std::cout << "✗ Agent thought event parsing failed" << std::endl;
    }
    
    auto event4 = deserializer.DeserializeStreamEvent(errorEvent);
    if (event4) {
        std::cout << "✓ Error event parsed successfully" << std::endl;
    } else {
        std::cout << "✗ Error event parsing failed" << std::endl;
    }
}

void TestFileResponseDeserialization() {
    std::cout << "\n=== Testing File Response Deserialization ===" << std::endl;
    
    std::string fileJson = R"({
        "id": "file123",
        "name": "test_document.pdf",
        "size": 1024000,
        "file_type": "document",
        "extension": "pdf",
        "mime_type": "application/pdf",
        "created_by": "user456",
        "created_at": 1640995200
    })";
    
    try {
        nlohmann::json j = nlohmann::json::parse(fileJson);
        FileResponse resp = FileResponseFromJson(j);
        
        std::cout << "✓ File response deserialized successfully" << std::endl;
        std::cout << "  File ID: " << resp.id_ << std::endl;
        std::cout << "  File name: " << resp.name_ << std::endl;
        std::cout << "  File size: " << resp.size_ << std::endl;
        std::cout << "  File type: " << static_cast<int>(resp.file_type_) << std::endl;
        
        // Verify no duplicate fields
        if (resp.extension_ == "pdf" && resp.mime_type_ == "application/pdf") {
            std::cout << "✓ No duplicate field assignments detected" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "✗ File response deserialization failed: " << e.what() << std::endl;
    }
}

void TestMalformedInput() {
    std::cout << "\n=== Testing Malformed Input Handling ===" << std::endl;
    
    StreamEventDeserializer deserializer;
    
    // Test empty string
    auto event1 = deserializer.DeserializeStreamEvent("");
    if (!event1) {
        std::cout << "✓ Empty string handled correctly" << std::endl;
    }
    
    // Test invalid JSON
    auto event2 = deserializer.DeserializeStreamEvent("invalid json");
    if (!event2) {
        std::cout << "✓ Invalid JSON handled correctly" << std::endl;
    }
    
    // Test JSON without event field
    auto event3 = deserializer.DeserializeStreamEvent(R"({"task_id":"123"})");
    if (!event3) {
        std::cout << "✓ JSON without event field handled correctly" << std::endl;
    }
    
    // Test incomplete SSE data
    auto event4 = deserializer.DeserializeStreamEvent("data:");
    if (!event4) {
        std::cout << "✓ Incomplete SSE data handled correctly" << std::endl;
    }
}

void TestConversationResponseDeserialization() {
    std::cout << "\n=== Testing Conversation Response Deserialization ===" << std::endl;
    
    std::string conversationJson = R"({
        "limit": 20,
        "has_more": true,
        "data": [{"id":"conv1","name":"Test Conversation"}]
    })";
    
    try {
        nlohmann::json j = nlohmann::json::parse(conversationJson);
        ConversationResponse resp = ConversationResponseFromJson(j);
        
        std::cout << "✓ Conversation response deserialized successfully" << std::endl;
        std::cout << "  Limit: " << resp.limit << std::endl;
        std::cout << "  Has more: " << (resp.has_more ? "true" : "false") << std::endl;
        std::cout << "  Data: " << resp.data << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "✗ Conversation response deserialization failed: " << e.what() << std::endl;
    }
}

int main() {
    std::cout << "Starting Deserialization Tests..." << std::endl;
    
    TestStreamEventDeserialization();
    TestFileResponseDeserialization();
    TestMalformedInput();
    TestConversationResponseDeserialization();
    
    std::cout << "\n=== All Deserialization Tests Completed ===" << std::endl;
    return 0;
}