#include <iostream>
#include <vector>
#include "libams/serializer/stream_event_deserializer.h"

using namespace amssdk;

void TestBasicStreamEventParsing() {
    std::cout << "=== Testing Basic Stream Event Parsing ===" << std::endl;
    
    StreamEventDeserializer deserializer;
    
    // Test direct JSON format
    std::string directJson = R"({"event":"message","task_id":"task123","message_id":"msg456","conversation_id":"conv789","answer":"Hello World"})";
    auto event1 = deserializer.DeserializeStreamEvent(directJson);
    
    if (event1 && event1->type() == StreamEvent::Type::MESSAGE) {
        auto* msg_event = event1->as<MessageEvent>();
        if (msg_event && msg_event->Answer() == "Hello World") {
            std::cout << "✓ Direct JSON message event parsed successfully" << std::endl;
        } else {
            std::cout << "✗ Direct JSON message event content mismatch" << std::endl;
        }
    } else {
        std::cout << "✗ Direct JSON message event parsing failed" << std::endl;
        std::cout << "  Error: " << deserializer.GetLastError() << std::endl;
    }
}

void TestSSEFormatParsing() {
    std::cout << "\n=== Testing SSE Format Parsing ===" << std::endl;
    
    StreamEventDeserializer deserializer;
    
    // Test SSE format
    std::string sseFormat = "data: {\"event\":\"agent_thought\",\"id\":\"thought1\",\"task_id\":\"task123\",\"message_id\":\"msg456\",\"conversation_id\":\"conv789\",\"thought\":\"Processing...\",\"tool\":\"calculator\",\"tool_input\":\"2+2\",\"position\":1}\n\n";
    
    auto event = deserializer.DeserializeStreamEvent(sseFormat);
    if (event && event->type() == StreamEvent::Type::AGENT_THOUGHT) {
        auto* thought_event = event->as<AgentThoughtEvent>();
        if (thought_event && thought_event->Thought() == "Processing...") {
            std::cout << "✓ SSE format agent thought event parsed successfully" << std::endl;
        } else {
            std::cout << "✗ SSE format agent thought event content mismatch" << std::endl;
        }
    } else {
        std::cout << "✗ SSE format agent thought event parsing failed" << std::endl;
        std::cout << "  Error: " << deserializer.GetLastError() << std::endl;
    }
}

void TestMultipleSSEEvents() {
    std::cout << "\n=== Testing Multiple SSE Events ===" << std::endl;
    
    StreamEventDeserializer deserializer;
    
    // Multiple SSE events in one chunk
    std::string multipleEvents = 
        "data: {\"event\":\"message\",\"task_id\":\"task1\",\"message_id\":\"msg1\",\"conversation_id\":\"conv1\",\"answer\":\"First message\"}\n\n"
        "data: {\"event\":\"message\",\"task_id\":\"task2\",\"message_id\":\"msg2\",\"conversation_id\":\"conv2\",\"answer\":\"Second message\"}\n\n"
        "data: {\"event\":\"message_end\",\"task_id\":\"task1\",\"message_id\":\"msg1\",\"conversation_id\":\"conv1\"}\n\n";
    
    auto events = deserializer.DeserializeMultipleEvents(multipleEvents);
    
    std::cout << "Parsed " << events.size() << " events from SSE chunk" << std::endl;
    
    for (size_t i = 0; i < events.size(); ++i) {
        if (events[i]) {
            std::cout << "  Event " << i << ": Type=" << static_cast<int>(events[i]->type()) << std::endl;
        }
    }
    
    const auto& stats = deserializer.GetStats();
    std::cout << "  Total events parsed: " << stats.total_events_parsed << std::endl;
    std::cout << "  Successful parses: " << stats.successful_parses << std::endl;
    std::cout << "  SSE events: " << stats.sse_events_parsed << std::endl;
}

void TestErrorHandling() {
    std::cout << "\n=== Testing Error Handling ===" << std::endl;
    
    StreamEventDeserializer deserializer;
    
    // Test empty string
    auto event1 = deserializer.DeserializeStreamEvent("");
    if (!event1) {
        std::cout << "✓ Empty string handled correctly: " << deserializer.GetLastError() << std::endl;
    }
    
    // Test invalid JSON
    auto event2 = deserializer.DeserializeStreamEvent("invalid json");
    if (!event2) {
        std::cout << "✓ Invalid JSON handled correctly: " << deserializer.GetLastError() << std::endl;
    }
    
    // Test JSON without event field
    auto event3 = deserializer.DeserializeStreamEvent(R"({"task_id":"123"})");
    if (!event3) {
        std::cout << "✓ JSON without event field handled correctly: " << deserializer.GetLastError() << std::endl;
    }
    
    // Test unknown event type
    auto event4 = deserializer.DeserializeStreamEvent(R"({"event":"unknown_type"})");
    if (!event4) {
        std::cout << "✓ Unknown event type handled correctly: " << deserializer.GetLastError() << std::endl;
    }
    
    // Test malformed SSE
    auto event5 = deserializer.DeserializeStreamEvent("data:not_valid_json");
    if (!event5) {
        std::cout << "✓ Malformed SSE handled correctly: " << deserializer.GetLastError() << std::endl;
    }
}

void TestComplexEventTypes() {
    std::cout << "\n=== Testing Complex Event Types ===" << std::endl;
    
    StreamEventDeserializer deserializer;
    
    // Test error event
    std::string errorEvent = R"({"event":"error","task_id":"task123","message_id":"msg456","status":500,"code":"INTERNAL_ERROR","message":"Something went wrong"})";
    auto error_event = deserializer.DeserializeStreamEvent(errorEvent);
    if (error_event && error_event->type() == StreamEvent::Type::ERR) {
        auto* err_event = error_event->as<ErrorEvent>();
        if (err_event && err_event->Status() == 500 && err_event->Code() == "INTERNAL_ERROR") {
            std::cout << "✓ Error event parsed successfully" << std::endl;
        }
    }
    
    // Test workflow event
    std::string workflowEvent = R"({"event":"workflow_started","task_id":"task123","workflow_run_id":"wf456","data":{"step":"initialization"}})";
    auto workflow_event = deserializer.DeserializeStreamEvent(workflowEvent);
    if (workflow_event && workflow_event->type() == StreamEvent::Type::WORKFLOW_STARTED) {
        std::cout << "✓ Workflow event parsed successfully" << std::endl;
    }
    
    // Test TTS event
    std::string ttsEvent = R"({"event":"tts_message","task_id":"task123","message_id":"msg456","audio":"base64_audio_data","created_at":1640995200})";
    auto tts_event = deserializer.DeserializeStreamEvent(ttsEvent);
    if (tts_event && tts_event->type() == StreamEvent::Type::TTS_MESSAGE) {
        std::cout << "✓ TTS message event parsed successfully" << std::endl;
    }
}

void TestPerformance() {
    std::cout << "\n=== Testing Performance ===" << std::endl;
    
    StreamEventDeserializer deserializer;
    
    // Create a large batch of events
    std::string largeBatch;
    for (int i = 0; i < 1000; ++i) {
        largeBatch += "data: {\"event\":\"message\",\"task_id\":\"task" + std::to_string(i) + "\",\"message_id\":\"msg" + std::to_string(i) + "\",\"conversation_id\":\"conv" + std::to_string(i) + "\",\"answer\":\"Message " + std::to_string(i) + "\"}\n\n";
    }
    
    auto start = std::chrono::high_resolution_clock::now();
    auto events = deserializer.DeserializeMultipleEvents(largeBatch);
    auto end = std::chrono::high_resolution_clock::now();
    
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    std::cout << "✓ Parsed " << events.size() << " events in " << duration.count() << "ms" << std::endl;
    std::cout << "  Average: " << (duration.count() / 1000.0) << "ms per event" << std::endl;
}

int main() {
    std::cout << "Starting Stream Event Tests..." << std::endl;
    
    TestBasicStreamEventParsing();
    TestSSEFormatParsing();
    TestMultipleSSEEvents();
    TestErrorHandling();
    TestComplexEventTypes();
    TestPerformance();
    
    std::cout << "\n=== All Stream Event Tests Completed ===" << std::endl;
    return 0;
}